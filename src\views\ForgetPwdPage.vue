<template>
  <div class="forget-pwd-container">
    <div class="forget-pwd-left-box">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="forget-pwd-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" />

      <!-- 步骤一：验证身份 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="welcome-text">
          <span class="title-text">验证身份</span>
        </div>

        <el-form :model="verifyForm" class="forget-pwd-form">
          <!-- 手机号输入框 -->
          <el-form-item>
            <el-input
              v-model="verifyForm.phone"
              placeholder="手机号"
              class="form-input"
              size="large"
            />
          </el-form-item>

          <!-- 验证码输入框 -->
          <el-form-item>
            <el-input
              v-model="verifyForm.verifyCode"
              placeholder="验证码"
              class="form-input verify-code-input"
              size="large"
            >
              <template #suffix>
                <el-button
                  type="text"
                  class="send-code-btn"
                  @click="sendVerifyCode"
                >
                  发送验证码
                </el-button>
              </template>
            </el-input>
          </el-form-item>

          <!-- 图形验证码 -->
          <el-form-item>
            <el-input
              v-model="verifyForm.captcha"
              placeholder="验证码"
              class="form-input captcha-input"
              size="large"
            >
              <template #suffix>
                <div class="captcha-image">
                  <span class="captcha-text">JHWi</span>
                </div>
              </template>
            </el-input>
          </el-form-item>

          <!-- 下一步按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              class="next-btn"
              size="large"
              @click="handleNextStep"
            >
              下一步
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤二：修改密码 -->
      <div v-else class="step-content">
        <div class="welcome-text">
          <span class="title-text">修改密码</span>
        </div>

        <el-form :model="passwordForm" class="forget-pwd-form">
          <!-- 新密码输入框 -->
          <el-form-item>
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="密码"
              class="form-input"
              size="large"
              show-password
            />
          </el-form-item>

          <!-- 确认密码输入框 -->
          <el-form-item>
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              class="form-input"
              size="large"
              show-password
            />
          </el-form-item>

          <!-- 按钮组 -->
          <el-form-item>
            <div class="button-group">
              <el-button
                class="prev-btn"
                size="large"
                @click="handlePrevStep"
              >
                上一步
              </el-button>
              <el-button
                type="primary"
                class="submit-btn"
                size="large"
                @click="handleSubmit"
              >
                提交
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 当前步骤：1-验证身份，2-修改密码
const currentStep = ref(1)

// 验证身份表单数据
const verifyForm = ref({
  phone: '',
  verifyCode: '',
  captcha: ''
})

// 修改密码表单数据
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

// 发送验证码
const sendVerifyCode = () => {
  if (!verifyForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  // TODO: 调用发送验证码接口
  ElMessage.success('验证码已发送')
}

// 下一步
const handleNextStep = () => {
  if (!verifyForm.value.phone) {
    ElMessage.warning('请输入手机号')
    return
  }
  if (!verifyForm.value.verifyCode) {
    ElMessage.warning('请输入验证码')
    return
  }
  if (!verifyForm.value.captcha) {
    ElMessage.warning('请输入图形验证码')
    return
  }

  // TODO: 验证身份接口调用
  currentStep.value = 2
}

// 上一步
const handlePrevStep = () => {
  currentStep.value = 1
}

// 提交修改密码
const handleSubmit = () => {
  if (!passwordForm.value.newPassword) {
    ElMessage.warning('请输入新密码')
    return
  }
  if (!passwordForm.value.confirmPassword) {
    ElMessage.warning('请确认密码')
    return
  }
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  // TODO: 调用修改密码接口
  ElMessage.success('密码修改成功')
  router.push('/login')
}
</script>

<style scoped>
.forget-pwd-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.forget-pwd-left-box {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  position: relative;
  overflow: hidden;
}

.forget-pwd-left-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../assets/bg-pattern.png') no-repeat center center;
  background-size: cover;
  opacity: 0.1;
}

.main-logo {
  width: 400px;
  height: auto;
  z-index: 1;
}

.forget-pwd-right-box {
  width: 400px;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.logo {
  width: 120px;
  height: auto;
  margin-bottom: 30px;
}

.step-content {
  width: 100%;
}

.welcome-text {
  text-align: center;
  margin-bottom: 30px;
}

.title-text {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.forget-pwd-form {
  width: 100%;
}

.form-input {
  margin-bottom: 20px;
}

.form-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: none;
  padding: 12px 16px;
  height: 48px;
}

.form-input :deep(.el-input__wrapper:hover) {
  border-color: #2f7dfb;
}

.form-input :deep(.el-input__wrapper.is-focus) {
  border-color: #2f7dfb;
  box-shadow: 0 0 0 2px rgba(47, 125, 251, 0.1);
}

.verify-code-input :deep(.el-input__suffix) {
  padding-right: 8px;
}

.send-code-btn {
  color: #2f7dfb;
  font-size: 14px;
  padding: 0;
  height: auto;
  border: none;
}

.send-code-btn:hover {
  color: #125cd2;
}

.captcha-input :deep(.el-input__suffix) {
  padding-right: 8px;
}

.captcha-image {
  width: 60px;
  height: 30px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.captcha-text {
  font-size: 14px;
  color: #666;
  font-weight: bold;
  letter-spacing: 2px;
}

.next-btn {
  width: 100%;
  height: 48px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  font-size: 16px;
  font-weight: 500;
}

.next-btn:hover {
  background: linear-gradient(180deg, #4a8bfc 0%, #2366d4 100%);
}

.button-group {
  display: flex;
  gap: 10px;
  width: 100%;
}

.prev-btn {
  flex: 1;
  height: 48px;
  border-radius: 10px;
  background: var(--fill-3, #E5E6EB);
  border: none;
  color: #666;
  font-size: 16px;
  font-weight: 500;
}

.prev-btn:hover {
  background: #d9dadf;
}

.submit-btn {
  flex: 1;
  height: 48px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%), linear-gradient(0deg, #2F7DFB 0%, #2F7DFB 100%), linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  font-size: 16px;
  font-weight: 500;
}

.submit-btn:hover {
  background: linear-gradient(180deg, #4a8bfc 0%, #2366d4 100%);
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .forget-pwd-container {
    flex-direction: column;
  }

  .forget-pwd-left-box {
    height: 200px;
    flex: none;
  }

  .main-logo {
    width: 250px;
  }

  .forget-pwd-right-box {
    width: 100%;
    flex: 1;
    padding: 20px;
  }

  .logo {
    width: 80px;
    margin-bottom: 20px;
  }

  .title-text {
    font-size: 20px;
  }
}
</style>