<template>
  <div class="page-container">
    <!-- 移动端顶部白色区域 -->
    <div class="mobile-header" v-if="isMobile">
      <div class="mobile-header-top">
        <div class="mobile-brand">
          <img src="../assets/logo.svg" alt="logo" class="mobile-logo" />
        </div>
        <div class="mobile-menu" @click="toggleMobileMenu">
          <div class="menu-dots" :class="{ 'menu-expanded': showMobileMenu }">
            <img :src="showMobileMenu ? '/src/assets/top_nounfold.png' : '/src/assets/top_unfold.png'" width="20px" height="20px" />
          </div>
        </div>
      </div>
      <!-- 展开的菜单项 -->
      <div class="mobile-menu-items" v-if="showMobileMenu">
        <div class="mobile-menu-item" @click="handleMobileLogin">登录</div>
        <div class="mobile-menu-item" @click="handleMobileRegister">注册</div>
      </div>
    </div>

    <div class="home-container" ref="homeSection">
      <div class="topbar" v-if="!isMobile">
        <img src="../assets/logo.svg" alt="logo" class="logo" />
        <div class="nav">
          <span class="nav-item" @click="scrollToSection('home')">首页</span>
          <span class="nav-item" @click="scrollToSection('about')">关于我们</span>
          <span class="nav-item" @click="scrollToSection('services')">应用服务</span>
          <span class="nav-item" @click="scrollToSection('experience')">服务体验</span>
          <span class="nav-item" @click="scrollToSection('subscribe')">订阅</span>
        </div>
        <div class="topbutton">
          <span class="login-btn" @click="handleLogin">登录</span>
          <el-button type="primary" class="register-btn" @click="handleRegister">注册</el-button>
        </div>
      </div>
      <div class="main-content">
        <div class="center-content">
          <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
          <el-button size="large" class="experience-btn">
            立即体验
          </el-button>
        </div>
      </div>
    </div>
    <second-page ref="aboutSection"/>
    <third-page/>
    <fourth-page ref="servicesSection"/>
    <fifth-page ref="experience"/>
    <sixth-page ref="sixthSection"/>
    <last-page/>
  </div>  
</template>

<script setup lang="ts">
import { ElButton } from 'element-plus';
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { useRouter } from 'vue-router'
import SecondPage from './SecondPage.vue';
import ThirdPage from './ThirdPage.vue';
import FourthPage from './FourthPage.vue';
import FifthPage from './FifthPage.vue';
import SixthPage from './SixthPage.vue';
import LastPage from './LastPage.vue';

// 路由实例
const router = useRouter()

// 移动端相关状态
const isMobile = ref(false)
const showMobileMenu = ref(false)

const homeSection = ref<HTMLElement>()
const aboutSection = ref<InstanceType<typeof SecondPage>>()
const servicesSection = ref<InstanceType<typeof FourthPage>>()
const experience = ref<InstanceType<typeof FifthPage>>()
const sixthSection = ref<InstanceType<typeof SixthPage>>()

// 移动端相关方法
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleMobileLogin = () => {
  router.push('/login')
  closeMobileMenu()
}

const handleMobileRegister = () => {
  router.push('/register')
  closeMobileMenu()
}

// 桌面端登录注册处理函数
const handleLogin = () => {
  router.push('/login')
}

const handleRegister = () => {
  router.push('/register')
}

// 生命周期钩子
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', checkMobile)
})

const scrollToSection = (sectionName: string) => {
  let targetElement: HTMLElement | null = null

  switch (sectionName) {
    case 'home':
      targetElement = homeSection.value
      break
    case 'about':
      targetElement = aboutSection.value?.$el
      break
    case 'services':
      targetElement = servicesSection.value?.$el
      break
    case 'experience':
      targetElement = experience.value?.$el
      break
    case 'subscribe':
      targetElement = sixthSection.value?.$el
      break
    default:
      targetElement = homeSection.value
  }

  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

onMounted(() => {

})
</script>

<style>
/* 全局隐藏滚动条 */
html, body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}
</style>

<style scoped>
.page-container {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.home-container {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  margin: 10px 24px 0px 24px;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.topbar {
  /* padding: 0px 24px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  height: 68px;
  margin-top: -5px;
}

.logo {
  height: 32px;
  width: auto;
}

.nav {
  display: flex;
  gap: 40px;
  align-items: center;
}

.nav-item {
  color: break;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 8px 0;
}

.nav-item:hover {
  color: #5B9BD5;
}

.topbutton {
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-btn {
  display: flex;
  padding: 7px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #5B9BD5;
}

.register-btn {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.20);
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  display: flex;
  padding: 7px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.register-btn:hover {
  border: 1px solid rgba(255, 255, 255, 0.40);
}

.main-content {
  border-radius: 10px;
  background-image: url('../assets/banner.png');
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 980px;
  padding: 0 24px;
}

.center-content {
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.main-logo {
  max-width: 600px;
  width: 100%;
  height: auto;
  margin-bottom: 30px;
  margin-top: 180px;
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  letter-spacing: 2px;
}

.experience-btn {
  font-size: 16px;
  padding: 16px 40px;
  background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
  border: 1px solid rgba(255, 255, 255, 0.20);
  border-radius: 10px;
  font-family: "江城斜黑体", sans-serif;
  font-weight: 500;
  width: 154px;
  height: 48px;
  margin-top: 120px;
  text-align: center;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  -webkit-text-stroke-width: 0.2px;
  -webkit-text-stroke-color: rgba(255, 255, 255, 0.60);
  font-style: normal;
  line-height: normal;
  letter-spacing: 3.2px;
}


:deep(.experience-btn span) {
  background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.40) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.experience-btn::before {
  background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.40) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 移动端白色顶部区域 */
.mobile-header {
  background: white;
  margin: 12px 12px 0 12px;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.mobile-header-top {
  height: 108px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.mobile-brand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-logo {
  height: 32px;
  width: auto;
}

.mobile-menu {
  cursor: pointer;
  padding: 8px;
}

.menu-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.menu-dots img {
  transition: all 0.3s ease;
}

/* 移动端展开菜单样式 */
.mobile-menu-items {
  padding: 0 20px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

.mobile-menu-item {
  padding: 15px 0;
  font-size: 16px;
  color: #333;
  cursor: pointer;
  text-align: left;
  border-bottom: 1px solid #f5f5f5;
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-menu-item:hover {
  color: #2F7DFB;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 0;
    margin: 0;
  }

  .home-container {
    margin: 0 12px 12px 12px;
    border-radius: 20px;
    min-height: calc(100vh - 132px);
    background-image: url('../assets/banner.png');
    background-size: cover;
    background-position: center;
    position: relative;
  }

  .topbar {
    display: none; /* 在移动端隐藏桌面端导航栏 */
  }

  .main-content {
    padding: 0 20px;
    min-height: 600px;
    background: none;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: 20px;
  }

  .center-content {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
  }

  .main-logo {
    max-width: 280px;
    width: 90%;
    height: auto;
    margin-bottom: 40px;
    margin-top: 60px;
  }

  .subtitle {
    font-size: 14px;
    margin-bottom: 30px;
    letter-spacing: 1px;
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    display: inline-block;
  }

  .experience-btn {
    font-size: 16px;
    padding: 16px 40px;
    background: linear-gradient(180deg, #2F7DFB 0%, #125CD2 100%);
    border: 1px solid rgba(255, 255, 255, 0.20);
    border-radius: 10px;
    font-family: "江城斜黑体", sans-serif;
    font-weight: 500;
    width: 154px;
    height: 48px;
    margin-top: 120px;
    text-align: center;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    -webkit-text-stroke-width: 0.2px;
    -webkit-text-stroke-color: rgba(255, 255, 255, 0.60);
    font-style: normal;
    line-height: normal;
    letter-spacing: 3.2px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .experience-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(47, 125, 251, 0.4);
  }
}

/* 小屏幕手机适配 */
@media (max-width: 480px) {
  .mobile-header {
    margin: 8px 8px 0 8px;
    border-radius: 16px 16px 0 0;
  }

  .mobile-header-top {
    height: 96px;
    padding: 0 16px;
  }

  .mobile-logo {
    height: 28px;
  }

  .mobile-menu-items {
    padding: 0 16px 16px 16px;
  }

  .home-container {
    margin: 0 8px 8px 8px;
    border-radius: 16px;
    min-height: 500px;
  }

  .main-content {
    padding: 0 16px;
    min-height: 500px;
  }

  .main-logo {
    max-width: 240px;
    width: 85%;
    margin-bottom: 30px;
    margin-top: 40px;
  }

  .subtitle {
    font-size: 12px;
    padding: 6px 12px;
    margin-bottom: 25px;
  }

  .experience-btn {
    width: 140px;
    height: 44px;
    font-size: 14px;
    padding: 12px 30px;
    margin-top: 50px;
    letter-spacing: 2.8px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .mobile-header {
    margin: 6px 6px 0 6px;
    border-radius: 12px 12px 0 0;
  }

  .mobile-header-top {
    height: 88px;
    padding: 0 12px;
  }

  .mobile-logo {
    height: 24px;
  }

  .mobile-menu-items {
    padding: 0 12px 12px 12px;
  }

  .home-container {
    margin: 0 6px 6px 6px;
    border-radius: 12px;
    min-height: 450px;
  }

  .main-content {
    min-height: 450px;
  }

  .main-logo {
    max-width: 200px;
    width: 80%;
  }

  .experience-btn {
    width: 130px;
    height: 40px;
    font-size: 13px;
    padding: 10px 25px;
    letter-spacing: 2.4px;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
</style>