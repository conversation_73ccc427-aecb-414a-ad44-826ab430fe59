<template>
  <div class="login-container">
    <div class="login-left-box">
      <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
    </div>
    <div class="login-right-box">
      <img src="../assets/logo.svg" alt="logo" class="logo" />
      <span>hello!</span>
      <span>欢迎登录热爱元宇宙</span>
      <el-input class="user-input"></el-input>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped>
.login-container {
  margin: 10px;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: row;
}

.login-left-box {
  background-image: url('../assets/banner.png');
  width: 1184px;
  height: 940px;
  background-size: cover;
  background-position: center;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.main-logo {
  width: 722px;
  height: 338px;
}

.login-right-box {
  display: flex;
  width: 706px;
  padding: 40px;
  flex-direction: column;
  flex-shrink: 0;
  align-self: stretch;
  border-radius: 20px;
  background: #ffffff;
}

.login-right-box > span {
  color: var(--text-1, #1D2129);
  font-family: "江城斜黑体", sans-serif;
  font-size: 40px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.logo {
  height: 45px;
  width: auto;
  align-self: flex-start;
  padding-bottom: 100px;
}

.user-input {
  height: 40px;
  border-radius: 10px;
  border: 1px solid var(--border-2, #E5E6EB);
  background: var(--fill-2, #F2F3F5);
  margin-top: 40px;
}

</style>